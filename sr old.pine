//@version=6
// ====================================================================
// BrandonJames - S/R Pro v4.5 (Enhanced Edition)
// ====================================================================
// This indicator identifies support and resistance zones (order blocks) based on
// price action, volume, and market structure. It provides visual customization,
// and reversal detection with improved performance and additional features for
// better trading decisions. Now includes a Compact Mode for cleaner chart display
// with minimal information.
// ====================================================================

indicator("BrandonJames - S/R Pro v4.5.1", shorttitle="[BrandonJames] S/R Pro v4.5.1", overlay=true, max_bars_back=5000)

// ====================================================================
// INPUT PARAMETERS - ORGANIZED BY FUNCTIONALITY
// ====================================================================

// ----- Core Settings -----
group_core = "⚙️ Core Settings"
max_blocks  = input.int(7, "📦 Max Blocks Displayed", minval=1, maxval=20, group=group_core, tooltip="Maximum number of order blocks displayed on chart")
analysis_blocks = input.int(15, "🔍 Analysis Blocks", minval=5, maxval=50, group=group_core, tooltip="Number of blocks to keep in memory for analysis (higher values improve detection quality)")
star_selection = input.string("⭐⭐", "⭐ Min Star Rating", options=["⭐", "⭐⭐", "⭐⭐⭐", "⭐⭐⭐⭐", "⭐⭐⭐⭐⭐"], group=group_core, tooltip="Filter orderblocks by minimum star rating (1-5 stars)")
min_star_rating = switch star_selection
    "⭐" => 1
    "⭐⭐" => 2
    "⭐⭐⭐" => 3
    "⭐⭐⭐⭐" => 4
    "⭐⭐⭐⭐⭐" => 5
extendObs   = input.int(5, "➡️ Extend Right", minval=1, maxval=50, group=group_core, tooltip="Extend orderblocks to right after price")
debugMode   = input.bool(false, "  Debug Mode", group=group_core)

// ----- Lookback Settings -----
group_lookback = "🔍 Lookback Settings"
lookbackPeriod     = input.int(6, "🎯 Lookback Period", minval=2, group=group_lookback, tooltip="Number of bars for pivot calculation")
dynamicATRPeriod   = input.int(14, "ATR Period", group=group_lookback, tooltip="ATR period used for calculations")

// ----- Block Appearance -----
group_block_appearance = "📏 Block Appearance"
showPriceLabels      = input.bool(true, "🏷️ Price Labels", group=group_block_appearance)
showMidline          = input.bool(true, "➖ Show Midlines", group=group_block_appearance)
useDynamicBox        = input.bool(true, "🔄 Dynamic Size", group=group_block_appearance, tooltip="Adjust width based on volatility")
adaptiveBoxSize      = input.bool(true, "📊 Adaptive Size", group=group_block_appearance, tooltip="Automatically adjust box size based on market conditions")
box_width            = input.float(1, "📏 Base Width", minval=0, step=0.1, group=group_block_appearance, tooltip="ATR multiplier for box size")
dynamicBoxMultiplier = input.float(1.0, "🌀 Multiplier", minval=0.1, step=0.1, group=group_block_appearance, tooltip="ATR multiplier for dynamic width")
minBoxSize           = input.float(0.5, "🔻 Min Size", minval=0.1, step=0.1, group=group_block_appearance, tooltip="Minimum box size as ATR multiplier")
maxBoxSize           = input.float(8.0, "🔺 Max Size", minval=1.0, step=0.5, group=group_block_appearance, tooltip="Maximum box size as ATR multiplier")
boxSizeVolatilityWeight = input.float(0.7, "📈 Volatility Weight", minval=0.0, maxval=1.0, step=0.1, group=group_block_appearance, tooltip="Weight of volatility in dynamic box size calculation")
midlineColor         = input.color(color.rgb(255,255,255,70), "Midline Color", group=group_block_appearance)

// ----- Block Behavior -----
group_block_behavior = "⚙️ Block Behavior"
hideOverlaps         = input.bool(true, "🚫 Hide Overlapping", group=group_block_behavior)
showBreakerBlocks    = input.bool(true, "💥 Show Broken", group=group_block_behavior)
showMitigated        = input.bool(false, "⚡ Show Mitigated", group=group_block_behavior)
useTrendFilter       = input.bool(false, "📈 Use Trend Filter (EMA200)", group=group_block_behavior, tooltip="Only create blocks if trend conditions are met: supports allowed when price is above EMA200, resistances when below EMA200")

// ----- Block Colors -----
group_block_colors = "🎨 Block Colors"
bullColor        = input.color(color.rgb(0,180,108,80), "🟢 Bullish", group=group_block_colors)
bearColor        = input.color(color.rgb(255,82,82,80), "🔴 Bearish", group=group_block_colors)
breakoutColor    = input.color(color.rgb(255,215,0,80), "💥 Broken", group=group_block_colors)
mitigatedColor   = input.color(color.rgb(128,128,128,80), "⚡ Mitigated", group=group_block_colors)

// ----- Visual Enhancements -----
group_visual            = "🎨 Visual Enhancements"
displayModeOpt          = input.string("Compact", "Display Mode", options=["Compact", "Detailed"], group=group_visual, tooltip="Compact mode shows only essential information, Detailed mode shows all information")
orderBlockTextSizeOpt   = input.string("small", "Block Text Size", options=["tiny", "small", "normal", "large"], group=group_visual)
orderBlockTextSize      = orderBlockTextSizeOpt == "tiny" ? size.tiny : orderBlockTextSizeOpt == "small" ? size.small : orderBlockTextSizeOpt == "normal" ? size.normal : size.large
orderBlockBorderStyleOpt = input.string("solid", "Border Style", options=["solid", "dashed", "dotted"], group=group_visual)
orderBlockBorderStyle   = orderBlockBorderStyleOpt == "solid" ? line.style_solid : orderBlockBorderStyleOpt == "dashed" ? line.style_dashed : line.style_dotted
orderBlockBorderWidth   = input.int(1, "Border Width", minval=1, group=group_visual)
applyBlockAgeFading     = input.bool(true, "Age Fading", group=group_visual, tooltip="If enabled, older blocks become more transparent")
labelOffset             = input.int(5, "Label Offset", group=group_visual, tooltip="Vertical offset for price labels to reduce overlap")
midlineLineWidth        = input.int(1, "Midline Width", minval=1, group=group_visual)
tableTextSizeOpt        = input.string("small", "Table Text Size", options=["tiny", "small", "normal", "large"], group=group_visual, tooltip="Text size for all tables (Star Rating, Statistics, etc.)")
tableTextSize           = tableTextSizeOpt == "tiny" ? size.tiny : tableTextSizeOpt == "small" ? size.small : tableTextSizeOpt == "normal" ? size.normal : size.large

// ----- Smart Filtering -----
group_smart = "🧠 Smart Filtering"
useSmartFiltering = input.bool(true, "Enable Smart Filtering", group=group_smart, tooltip="Filter out low-quality zones based on multiple criteria")
showZoneStrength = input.bool(true, "Show Zone Strength", group=group_smart, tooltip="Display strength rating for each zone")
minZoneStrength = input.float(1.2, "Min Zone Strength", minval=0.5, step=0.1, group=group_smart, tooltip="Minimum strength multiplier for a zone to be displayed")
useVolumeProfile = input.bool(true, "Use Volume Profile", group=group_smart, tooltip="Consider volume profile when evaluating zone strength")
minVolumeRatio = input.float(1.5, "Min Volume Ratio", minval=1.0, step=0.1, group=group_smart, tooltip="Minimum ratio of zone volume to average volume")
dynamicStarRating = input.bool(true, "Dynamic Star Rating", group=group_smart, tooltip="Adjust star rating threshold based on market volatility")
volatilityAdjustment = input.float(0.8, "Volatility Adjustment", minval=0.1, maxval=2.0, step=0.1, group=group_smart, tooltip="Strength of volatility adjustment (higher values = stronger adjustment)")

// ----- Confluence Detection -----
group_confluence = "🎯 Confluence Detection"
detectConfluence = input.bool(true, "Detect Confluence", group=group_confluence, tooltip="Highlight zones that align with other technical factors")
highlightConfluence = input.bool(true, "Highlight Confluence Zones", group=group_confluence, tooltip="Apply special highlighting to zones with confluence")
prioritizeConfluence = input.bool(true, "Prioritize Confluence", group=group_confluence, tooltip="Prioritize blocks with confluence factors when filtering")
confluenceBoost = input.float(1.5, "Confluence Boost", minval=1.0, maxval=3.0, step=0.1, group=group_confluence, tooltip="Boost strength score for blocks with confluence")
useRSI = input.bool(true, "Use RSI", group=group_confluence, tooltip="Consider RSI levels for confluence")
rsiPeriod = input.int(14, "RSI Period", minval=1, group=group_confluence)
rsiOverbought = input.int(70, "RSI Overbought", minval=50, maxval=100, group=group_confluence)
rsiOversold = input.int(30, "RSI Oversold", minval=0, maxval=50, group=group_confluence)
useMACD = input.bool(true, "Use MACD", group=group_confluence, tooltip="Consider MACD for confluence")
macdFastLength = input.int(12, "MACD Fast Length", minval=1, group=group_confluence)
macdSlowLength = input.int(26, "MACD Slow Length", minval=1, group=group_confluence)
macdSignalLength = input.int(9, "MACD Signal Length", minval=1, group=group_confluence)

// ----- Volume Settings -----
group_volume         = "📊 Volume Settings"
volMultiplier        = input.float(1.2, "📉 Threshold", minval=0.1, step=0.1, group=group_volume, tooltip="Volume significance multiplier")
buySellDominanceThreshold = input.float(60.0, "🔵🔴 Buy/Sell Dominance %", minval=50, maxval=100, step=1, group=group_volume, tooltip="Threshold to determine buy/sell dominance")
vol_len              = input.int(2, "📈 Smoothing", minval=1, group=group_volume, tooltip="Bars for volume filtering")



// ----- Time Filtering -----
group_time_filter    = "⏰ Time Filtering"
enableTimeFilter     = input.bool(false, "Enable Time Filtering", group=group_time_filter, tooltip="Filter out older blocks based on age")
maxBlockAge          = input.int(100, "Max Block Age (bars)", minval=10, maxval=500, group=group_time_filter, tooltip="Maximum age in bars for blocks to be displayed")



// ----- Reversal Detection -----
group_reversal = "🔄 Reversal Detection"
showReversalTriangles = input.bool(true, "🔼🔽 Show Reversals", group=group_reversal)
bullishReversalColor  = input.color(color.rgb(255,80,0,60), "🟢 Bullish", group=group_reversal)
bearishReversalColor  = input.color(color.rgb(0,255,128,60), "🔴 Bearish", group=group_reversal)

// ----- Moving Averages -----
group_ma    = "📈 Moving Averages"
show_ema200    = input.bool(false, "� EMA 200", group=group_ma, tooltip="200-period Exponential Moving Average")
ema200_source  = input.source(close, "EMA200 Source", group=group_ma)
ema200_length  = input.int(200, "EMA200 Length", minval=1, group=group_ma)
ema200_color   = input.color(color.rgb(0,128,255,80), "EMA200 Color", group=group_ma)

show_ema50   = input.bool(false, "🟣 EMA 50", group=group_ma, tooltip="50-period Exponential Moving Average")
ema50_source = input.source(close, "EMA50 Source", group=group_ma)
ema50_length = input.int(50, "EMA50 Length", minval=1, group=group_ma)
ema50_color  = input.color(color.rgb(178,102,255,80), "EMA50 Color", group=group_ma)

show_sma    = input.bool(false, "� SMA", group=group_ma, tooltip="Simple Moving Average")
sma_source  = input.source(high, "SMA Source", group=group_ma)
sma_length  = input.int(25, "SMA Length", minval=1, group=group_ma)
sma_color   = input.color(color.rgb(255,215,0,80), "SMA Color", group=group_ma)







// ====================================================================
// UTILITY FUNCTIONS
// ====================================================================

// Cleanup function to remove all visual elements of an order block
// This reduces code duplication and improves maintainability
f_cleanupOrderBlock(ob) =>
    if not na(ob)
        box.delete(ob.box)
        if not na(ob.midline)
            line.delete(ob.midline)
        if not na(ob.top_price_label)
            label.delete(ob.top_price_label)
        if not na(ob.bottom_price_label)
            label.delete(ob.bottom_price_label)
        if not na(ob.confluence_label)
            label.delete(ob.confluence_label)

// ====================================================================
// FUNCTIONS FOR MARKET REGIME DETECTION
// ====================================================================

// Improved ADX calculation that uses the built-in function correctly
f_adx(length) =>
    // Get ADX directly from the third return value of ta.dmi
    [_, _, adx] = ta.dmi(length, length)
    adx

// Detect market regime (trending, ranging, or volatile)
// Returns: 0 = ranging, 1 = trending, 2 = volatile
f_detectMarketRegime() =>
    // Calculate ADX for trend strength
    float adx_value = f_adx(14)

    // Calculate volatility using ATR relative to price
    float volatility = ta.atr(dynamicATRPeriod) / close * 100

    // Calculate price movement consistency
    int up_bars = 0
    int down_bars = 0
    for i = 0 to 9
        if close[i] > close[i+1]
            up_bars += 1
        else if close[i] < close[i+1]
            down_bars += 1

    float consistency = math.abs(up_bars - down_bars) / 10

    // Determine regime
    if adx_value > 30 and consistency > 0.6
        1  // Trending
    else if volatility > 2.5  // High volatility threshold (2.5% of price)
        2  // Volatile
    else
        0  // Ranging

// Get timeframe multiplier for lookback adaptation
f_getTimeframeMultiplier() =>
    float tf_minutes = timeframe.in_seconds() / 60

    if tf_minutes <= 5
        1.0  // Lower timeframes - use standard lookback
    else if tf_minutes <= 15
        1.2  // 15m
    else if tf_minutes <= 60
        1.5  // 1h
    else if tf_minutes <= 240
        2.0  // 4h
    else if tf_minutes <= 1440
        3.0  // Daily
    else
        4.0  // Weekly and above

// Get adaptive box size based on market conditions
f_getAdaptiveBoxSize() =>
    // Base calculation using ATR
    float base_size = ta.atr(dynamicATRPeriod) * dynamicBoxMultiplier
    float size_mult = 1.0  // Default multiplier (neutral)

    // Extract function call for consistency
    int box_regime = f_detectMarketRegime()

    // Adjust based on regime
    if box_regime == 1  // Trending
        // Wider boxes in trending markets
        size_mult := 1.3
    else if box_regime == 2  // Volatile
        // Wider boxes in volatile markets
        size_mult := 1.5
    else  // Ranging
        // Narrower boxes in ranging markets
        size_mult := 0.8

    // Extract ATR calculation for consistency
    float current_atr = ta.atr(dynamicATRPeriod)

    // Apply volatility weight with safety checks
    float atr_sma = ta.sma(current_atr, 50)
    float volatility_factor = atr_sma > 0 ? current_atr / atr_sma : 1.0
    float volatility_adjustment = 1.0 + math.max(-0.5, math.min(0.5, volatility_factor - 1.0)) * boxSizeVolatilityWeight

    // Calculate final size with bounds
    float final_size = base_size * size_mult * volatility_adjustment
    math.max(minBoxSize * current_atr, math.min(maxBoxSize * current_atr, final_size))

// ====================================================================
// FUNCTIONS FOR CONFLUENCE DETECTION AND ZONE STRENGTH
// ====================================================================

// Calculate RSI confluence
f_rsiConfluence(level, is_support) =>
    rsi_value = ta.rsi(close, rsiPeriod)
    if is_support
        rsi_value <= rsiOversold  // Oversold condition for support
    else
        rsi_value >= rsiOverbought  // Overbought condition for resistance

// Calculate MACD confluence
f_macdConfluence(level, is_support) =>
    [macdLine, signalLine, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
    if is_support
        macdLine < 0 and macdLine > signalLine  // Bullish crossover below zero
    else
        macdLine > 0 and macdLine < signalLine  // Bearish crossover above zero

// Calculate MA confluence
f_maConfluence(level, is_support) =>
    ema50_val = ta.ema(ema50_source, ema50_length)
    ema200_val = ta.ema(ema200_source, ema200_length)
    if is_support
        math.abs(level - ema50_val) / ema50_val < 0.01 or math.abs(level - ema200_val) / ema200_val < 0.01
    else
        math.abs(level - ema50_val) / ema50_val < 0.01 or math.abs(level - ema200_val) / ema200_val < 0.01

// Calculate volume confluence
f_volumeConfluence(volume) =>
    avgVolume = ta.sma(volume, 20)
    volume >= avgVolume * minVolumeRatio

// f_calculateZoneStrength() - Refactored for Efficiency
// Now accepts raw data instead of an OrderBlock object to avoid creating temp drawings.
f_calculateZoneStrength(float vol_ratio, bool rsi_conf, bool macd_conf, bool ma_conf, bool vol_conf) =>
    // Base strength from volume
    float volumeStrength = math.min(vol_ratio / minVolumeRatio, 2.0) * 2.5  // 0-5 points from volume

    // Confluence bonus
    float confluenceBonus = 0.0
    int confluenceCount = 0

    if rsi_conf
        confluenceBonus += 1.0
        confluenceCount += 1
    if macd_conf
        confluenceBonus += 1.0
        confluenceCount += 1
    if ma_conf
        confluenceBonus += 1.5
        confluenceCount += 1
    if vol_conf
        confluenceBonus += 1.5
        confluenceCount += 1

    // Apply confluence boost if enabled and block has confluence
    if prioritizeConfluence and confluenceCount > 0
        float boost_factor = 1.0 + (confluenceCount / 4.0) * (confluenceBoost - 1.0)
        confluenceBonus *= boost_factor

    // Calculate total score (0-10 scale)
    float totalScore = math.min(volumeStrength + confluenceBonus, 10.0)
    totalScore

// Get color-coded strength rating text
f_getColorCodedStarRating(strength) =>
    if strength >= 8.0
        "🟢🟢🟢🟢🟢"  // Green for strongest
    else if strength >= 6.0
        "🟢🟢🟢🟢⚪"
    else if strength >= 4.0
        "🟡🟡🟡⚪⚪"  // Yellow for medium
    else if strength >= 2.0
        "🟠🟠⚪⚪⚪"  // Orange for weaker
    else
        "🔴⚪⚪⚪⚪"  // Red for weakest

// Cache star rating thresholds for better performance
var float[] base_star_thresholds = array.new_float(5, 0.0)
if barstate.isfirst
    array.set(base_star_thresholds, 0, 0.0)  // 1 star
    array.set(base_star_thresholds, 1, 2.0)  // 2 stars
    array.set(base_star_thresholds, 2, 4.0)  // 3 stars
    array.set(base_star_thresholds, 3, 6.0)  // 4 stars
    array.set(base_star_thresholds, 4, 8.0)  // 5 stars

// Get star rating threshold based on minimum star rating input
// Optimized with cached thresholds and reduced calculations
f_getStarRatingThreshold(star_rating) =>
    // Get base threshold from cache (with bounds checking)
    int index = math.max(0, math.min(4, star_rating - 1))
    float threshold = array.get(base_star_thresholds, index)

    // Apply dynamic adjustment based on market volatility if enabled
    if dynamicStarRating and star_rating > 1
        // Calculate current volatility relative to recent history
        float current_atr = ta.atr(dynamicATRPeriod)
        float atr_sma = ta.sma(current_atr, 50)
        float volatility_factor = atr_sma > 0 ? current_atr / atr_sma : 1.0

        // Determine market regime for additional context
        int market_regime = f_detectMarketRegime()

        // Adjust threshold based on volatility and market regime
        float adjustment_factor = 1.0

        if market_regime == 2  // Volatile market
            // Lower threshold in volatile markets to show more blocks
            adjustment_factor := math.max(0.7, 1.0 - (volatility_factor - 1.0) * volatilityAdjustment)
        else if market_regime == 1  // Trending market
            // Slightly lower threshold in trending markets
            adjustment_factor := math.max(0.85, 1.0 - (volatility_factor - 1.0) * volatilityAdjustment * 0.5)

        threshold := threshold * adjustment_factor

    threshold

// ====================================================================
// FUNCTIONS FOR AUTOMATED SENSITIVITY OPTIMIZATION
// ====================================================================
// Optimized sum function using built-in math.sum
f_sum(src, len) =>
    math.sum(src, len)







// --- Enhanced Global Variables and Type Definition ---

// Enhanced OrderBlock type with additional properties
type OrderBlock
    // Core block properties
    box box
    float upper_level
    float lower_level
    bool is_support
    int left_index
    int created_at

    // Volume information
    float total_volume
    float buy_volume
    float sell_volume

    // State tracking
    bool is_broken
    bool is_mitigated
    bool is_display_hidden  // New flag to track blocks hidden for display but kept for analysis

    // Strength and quality metrics
    float strength_score
    float volume_ratio
    bool has_confluence
    int retest_count
    float success_rate
    bool strength_calculated  // Flag to track if strength has been calculated
    bool meets_min_rating     // Flag to track if block meets minimum rating

    // Confluence factors
    bool rsi_confluence
    bool macd_confluence
    bool ma_confluence
    bool volume_confluence

    // Visual elements
    line midline
    label top_price_label
    label bottom_price_label
    label confluence_label

    // Performance tracking
    bool was_successful
    int last_touched_bar
    bool success_evaluated

// Constants for success criteria
var int SUCCESS_BARS_REQUIRED = 20  // Number of bars an orderblock must remain intact after being touched to be considered successful

// Cache ATR calculation with proper initialization
var float atr = ta.atr(dynamicATRPeriod)
atr := nz(ta.atr(dynamicATRPeriod), atr) // Update with new value or keep previous if NA

// Initialize order blocks array
var order_blocks = array.new<OrderBlock>()
// Track filtered blocks count (needs to be updated in main code, not in functions)
var int filtered_blocks_count = 0
// Arrays to track filtered blocks from functions
var filtered_by_create = array.new<bool>(0)
var filtered_by_update = array.new<bool>(0)

// Efficient volume tracking
var float volSMA = 0.0

// Enhanced volume formatting with string caching for better performance
// Cache for formatted volume strings to avoid redundant calculations
var int MAX_CACHE_SIZE = 20  // Limit cache size to prevent memory issues
var float[] cached_volume_values = array.new_float(0)
var string[] cached_volume_strings = array.new_string(0)

// Initialize cache arrays on first bar to ensure they're properly set up
if barstate.isfirst
    array.clear(cached_volume_values)
    array.clear(cached_volume_strings)

formatVolume(vol) =>
    vol_val = nz(vol, 0)

    // Check if volume is already in cache
    string formatted_vol = ""
    int cache_index = -1

    // Only search cache if it's not empty
    if array.size(cached_volume_values) > 0
        for i = 0 to array.size(cached_volume_values) - 1
            if math.abs(array.get(cached_volume_values, i) - vol_val) < 0.01  // Allow small difference
                cache_index := i
                break

    // If found in cache, return cached string
    if cache_index >= 0 and cache_index < array.size(cached_volume_strings)
        formatted_vol := array.get(cached_volume_strings, cache_index)
    else
        // Format the volume
        if vol_val >= 1e9
            formatted_vol := str.format("{0}B", str.tostring(math.round(vol_val / 1e9, 2)))
        else if vol_val >= 1e6
            formatted_vol := str.format("{0}M", str.tostring(math.round(vol_val / 1e6, 2)))
        else if vol_val >= 1e3
            formatted_vol := str.format("{0}K", str.tostring(math.round(vol_val / 1e3, 2)))
        else
            formatted_vol := str.tostring(math.round(vol_val))

        // Add to cache if not too large, with safety checks
        if not na(formatted_vol) and not na(vol_val)  // Ensure values are valid
            if array.size(cached_volume_values) < MAX_CACHE_SIZE
                array.push(cached_volume_values, vol_val)
                array.push(cached_volume_strings, formatted_vol)
            else if array.size(cached_volume_values) > 0 and array.size(cached_volume_strings) > 0
                // Replace oldest entry (circular buffer approach) with safety checks
                array.shift(cached_volume_values)
                array.shift(cached_volume_strings)
                array.push(cached_volume_values, vol_val)
                array.push(cached_volume_strings, formatted_vol)

    formatted_vol

// Faster up/down volume calculation with single comparison
upAndDownVolume() =>
    close > open ? volume : -volume

// Optimized pivot calculation with proper error handling
calcPivots(src, lb) =>
    // Ensure lookback is valid and reasonable
    validLb = math.max(1, math.min(50, lb))  // Cap at 50 to prevent extreme values

    // Use try/catch approach for pivot calculations
    float pivotHigh = na
    float pivotLow = na

    // Calculate pivots with error handling
    if not na(src) and validLb > 0 and validLb < 100  // Additional safety check
        pivotHigh := ta.pivothigh(src, validLb, validLb)
        pivotLow := ta.pivotlow(src, validLb, validLb)

    [pivotHigh, pivotLow]

// Improved support and resistance calculation with caching and error handling
calcSupportResistance(src, lb, Vol) =>
    // Cache volume thresholds for efficiency
    float volThreshold = volSMA * volMultiplier

    // Get pivot points (only calculate once)
    [pivotHigh, pivotLow] = calcPivots(src, lb)

    // Calculate box width with enhanced adaptivity
    float width = 0.0
    if useDynamicBox
        if adaptiveBoxSize
            // Use the advanced adaptive box sizing
            width := f_getAdaptiveBoxSize()
        else
            // Use the standard dynamic box sizing
            local_atr = ta.atr(dynamicATRPeriod)
            width := nz(local_atr, atr) * dynamicBoxMultiplier
    else
        width := atr * box_width

    // Initialize variables with default values
    float supportLevel = na
    float supportLevelAlt = na
    float resistanceLevel = na
    float resistanceLevelAlt = na
    color sup_color = bullColor
    color res_color = bearColor
    bool breakout_res = false
    bool res_holds = false
    bool sup_holds = false
    bool breakout_sup = false

    // Volume requirement check
    bool volumeSignificant = nz(Vol, 0) > volThreshold

    // Set support levels if conditions met
    if not na(pivotLow) and volumeSignificant
        supportLevel := pivotLow
        supportLevelAlt := pivotLow - width

    // Set resistance levels if conditions met
    if not na(pivotHigh) and volumeSignificant
        resistanceLevel := pivotHigh
        resistanceLevelAlt := pivotHigh + width

    // Calculate breakouts and holds only if levels are valid
    if not na(resistanceLevel)
        breakout_res := ta.crossover(low, resistanceLevelAlt)
        res_holds := ta.crossunder(high, resistanceLevel)

    if not na(supportLevel)
        breakout_sup := ta.crossover(low, supportLevel)
        sup_holds := ta.crossunder(high, supportLevelAlt)

    // Return all values in a consistent order
    [supportLevel, supportLevelAlt, resistanceLevel, resistanceLevelAlt,
     sup_color, res_color, breakout_res, res_holds, sup_holds, breakout_sup, Vol]

// Get buy/sell dominance icon
getBuySellIcon(buy_pct, sell_pct) =>
    string icon = ""
    if buy_pct >= buySellDominanceThreshold
        icon := "🔵" // Blue circle for buy dominance
    else if sell_pct >= buySellDominanceThreshold
        icon := "🔴" // Red circle for sell dominance
    else
        icon := "🟡" // Yellow circle for neutral
    icon

getBuySellColor(buy_pct, sell_pct) =>
    color color = na
    if buy_pct >= buySellDominanceThreshold
        color := color.blue
    else if sell_pct >= buySellDominanceThreshold
        color := color.red
    else
        color := color.yellow
    color

// Create an order block with enhanced features
createOrderBlock(is_support, level, level_alt, total_vol, buy_vol, sell_vol, blockColor) =>
    // First, remove ineligible blocks
    if array.size(order_blocks) > 0
        to_remove = array.new_int(0)
        for i = 0 to array.size(order_blocks) - 1
            ob = array.get(order_blocks, i)
            if (ob.is_broken and not showBreakerBlocks) or (ob.is_mitigated and not showMitigated)
                array.push(to_remove, i)
        if array.size(to_remove) > 0
            for i = array.size(to_remove) - 1 to 0 by 1
                idx = array.get(to_remove, i)
                if idx >= 0 and idx < array.size(order_blocks)
                    ob = array.get(order_blocks, idx)
                    f_cleanupOrderBlock(ob)
                    array.remove(order_blocks, idx)
                    if debugMode
                        label.new(bar_index, close, "Ineligible Removed: " + (ob.is_support ? "Support" : "Resistance") + "\nAge: " + str.tostring(bar_index - ob.created_at), color=color.orange, style=label.style_label_down)

    // Enforce analysis_blocks limit by removing oldest blocks (last elements)
    // We keep more blocks in memory for analysis than we display
    if array.size(order_blocks) >= analysis_blocks
        while array.size(order_blocks) >= analysis_blocks
            oldest_ob = array.pop(order_blocks) // Remove the oldest block
            f_cleanupOrderBlock(oldest_ob)
            if debugMode
                label.new(bar_index, close, "Oldest Removed: " + (oldest_ob.is_support ? "Support" : "Resistance") + "\nAge: " + str.tostring(bar_index - oldest_ob.created_at), color=color.yellow, style=label.style_label_down)

    // Add new block
    upper_level = math.max(level, level_alt)
    lower_level = math.min(level, level_alt)
    float mid_level = (upper_level + lower_level) / 2  // Changed to float for explicit type
    left_index  = bar_index - lookbackPeriod

    // Calculate confluence factors
    rsi_conf = useRSI and detectConfluence ? f_rsiConfluence(mid_level, is_support) : false
    macd_conf = useMACD and detectConfluence ? f_macdConfluence(mid_level, is_support) : false
    ma_conf = detectConfluence ? f_maConfluence(mid_level, is_support) : false

    // Calculate volume ratio
    avgVolume = ta.sma(volume, 20)
    vol_ratio = avgVolume > 0 ? total_vol / avgVolume : 1.0
    vol_conf = useVolumeProfile ? f_volumeConfluence(total_vol) : false

    // Determine if zone has confluence
    has_conf = rsi_conf or macd_conf or ma_conf or vol_conf

    // Apply smart filtering if enabled
    bool should_continue = true
    if useSmartFiltering and vol_ratio < minVolumeRatio and not has_conf  // Skip creating this zone if it doesn't meet criteria
        should_continue := false

    // Only calculate strength score if needed (lazy evaluation)
    float strength_score = 5.0  // Default value
    bool strength_calculated = false
    bool meets_min_rating = true

    // Check if we need to calculate strength (if min_star_rating > 1 or smart filtering is enabled)
    if min_star_rating > 1 or useSmartFiltering
        // Calculate strength score by passing the required variables directly.
        // This is efficient and avoids creating/deleting temporary objects.
        strength_score := f_calculateZoneStrength(vol_ratio, rsi_conf, macd_conf, ma_conf, vol_conf)
        strength_calculated := true

        // Apply star rating filter
        float star_rating_threshold = f_getStarRatingThreshold(min_star_rating)
        meets_min_rating := strength_score >= star_rating_threshold

        // Skip creating this zone if it doesn't meet the star rating criteria
        if not meets_min_rating
            should_continue := false
            array.push(filtered_by_create, true)
        else
            array.push(filtered_by_create, false)

    if should_continue
        // Apply block age fading if enabled
        blockOpacity = applyBlockAgeFading ? 85 : color.t(blockColor)

        // Apply special coloring for confluence zones
        finalBlockColor = blockColor
        if highlightConfluence and has_conf
            finalBlockColor := is_support ? color.rgb(0, 255, 128, 70) : color.rgb(255, 128, 0, 70)

        colorWithOpacity = color.new(finalBlockColor, blockOpacity)

        // Visual improvements for blocks with 3D effect
        borderColorBase = is_support ? color.rgb(0,220,128) : color.rgb(255,100,100)

        // Apply special border for confluence zones
        if highlightConfluence and has_conf
            borderColorBase := is_support ? color.rgb(0,255,128) : color.rgb(255,128,0)

        new_box = box.new(
             left_index, upper_level,
             bar_index + extendObs, lower_level,
             border_color=borderColorBase,
             border_width=orderBlockBorderWidth,
             bgcolor=colorWithOpacity,
             text="",
             text_color=color.white,
             text_size=orderBlockTextSize,
             extend=extend.right
             )

        // Enhanced midline with gradient effect
        midlineObj = showMidline ? line.new(
             left_index, mid_level,
             bar_index + extendObs, mid_level,
             color=midlineColor,
             width=midlineLineWidth,
             style=line.style_dashed
             ) : na

        float label_y = mid_level + labelOffset

        // Improved label styling
        labelBgColor = color.new(color.black, 20)
        labelTextColor = color.white

        info_label = label.new(
             bar_index + extendObs, label_y,
             "",
             color=labelBgColor,
             style=label.style_label_left,
             textcolor=labelTextColor,
             size=orderBlockTextSize
             )

        label price_label_obj = na
        label top_price_label_obj = is_support ? na : info_label
        label bottom_price_label_obj = is_support ? info_label : na
        label strength_label_obj = na
        label buy_sell_label_obj = na
        label confluence_label_obj = na

        // Create confluence label if needed
        if has_conf and highlightConfluence
            confluence_text = "🎯 Confluence: "
            if rsi_conf
                confluence_text += "RSI "
            if macd_conf
                confluence_text += "MACD "
            if ma_conf
                confluence_text += "MA "
            if vol_conf
                confluence_text += "VOL "

            confluence_y = mid_level

            confluence_label_obj := label.new(
                 bar_index + extendObs - 5, confluence_y,
                 confluence_text,
                 color=color.new(color.black, 20),
                 style=label.style_label_left,
                 textcolor=color.rgb(255, 215, 0),
                 size=orderBlockTextSize
                 )

        array.unshift(order_blocks, OrderBlock.new(
             new_box,            // box box
             upper_level,        // float upper_level
             lower_level,        // float lower_level
             is_support,         // bool is_support
             left_index,         // int left_index
             bar_index,          // int created_at
             total_vol,          // float total_volume
             buy_vol,            // float buy_volume
             sell_vol,           // float sell_volume
             false,              // bool is_broken
             false,              // bool is_mitigated
             false,              // bool is_display_hidden
             strength_score,     // float strength_score
             vol_ratio,          // float volume_ratio
             has_conf,           // bool has_confluence
             0,                  // int retest_count
             0.0,                // float success_rate
             strength_calculated, // bool strength_calculated
             meets_min_rating,   // bool meets_min_rating
             rsi_conf,           // bool rsi_confluence
             macd_conf,          // bool macd_confluence
             ma_conf,            // bool ma_confluence
             vol_conf,           // bool volume_confluence
             midlineObj,         // line midline
             top_price_label_obj,    // label top_price_label
             bottom_price_label_obj, // label bottom_price_label
             confluence_label_obj,   // label confluence_label
             false,              // bool was_successful
             0,                  // int last_touched_bar
             false               // bool success_evaluated
             ))

        if debugMode
            label.new(bar_index, mid_level, "Created " + (is_support ? "Support" : "Resistance") +
                     "\nVol: " + formatVolume(total_vol) +
                     "\nConfluence: " + (has_conf ? "Yes" : "No"),
                     color=color.rgb(0,128,255,80), style=label.style_label_down)
// Update order blocks with enhanced features
updateOrderBlocks() =>
    if array.size(order_blocks) == 0
        na
    else
        total_volume = 0.0
        for ob in order_blocks
            total_volume += ob.total_volume

        to_remove = array.new_int(0)
        for i = 0 to array.size(order_blocks) - 1
            ob = array.get(order_blocks, i)

            // Check for retests and update performance metrics
            mid_level = (ob.upper_level + ob.lower_level) / 2
            price_range = math.abs(ob.upper_level - ob.lower_level)

            // Detect retests (price coming within 25% of the zone)
            is_retesting = false
            if ob.is_support
                is_retesting := low <= ob.upper_level and low >= ob.upper_level - price_range * 0.25 and not ob.is_broken
            else
                is_retesting := high >= ob.lower_level and high <= ob.lower_level + price_range * 0.25 and not ob.is_broken

            // Update retest count and track when the orderblock was last touched
            if is_retesting
                ob.retest_count := ob.retest_count + 1
                ob.last_touched_bar := bar_index

                // Reset success evaluation when touched again
                if ob.success_evaluated
                    ob.success_evaluated := false
                    ob.was_successful := false

                // Check if retest was successful (price bounced)
                retest_success = false
                if ob.is_support
                    retest_success := close > ob.upper_level - price_range * 0.1
                else
                    retest_success := close < ob.lower_level + price_range * 0.1

                // Update success rate
                if ob.retest_count > 0
                    current_success = ob.success_rate * (ob.retest_count - 1)
                    new_success = current_success + (retest_success ? 1 : 0)
                    ob.success_rate := new_success / ob.retest_count

            // Check if we need to evaluate success (orderblock was touched and enough bars have passed)
            if not ob.success_evaluated and ob.last_touched_bar > 0 and (bar_index - ob.last_touched_bar) >= SUCCESS_BARS_REQUIRED
                // If the orderblock survived for the required number of bars, mark it as successful
                ob.was_successful := true
                ob.success_evaluated := true
                if debugMode
                    label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level,
                             "Success: Survived " + str.tostring(SUCCESS_BARS_REQUIRED) + " bars",
                             color=color.rgb(0,255,0), style=label.style_label_down)

            // Update status using low/high for breaks
            if not ob.is_broken
                // Check if block is broken
                if (ob.is_support and close[1] < ob.lower_level) or (not ob.is_support and close[1] > ob.upper_level)
                    ob.is_broken := true

                    // Check if this block was touched and failed within the required period
                    if ob.last_touched_bar > 0 and not ob.success_evaluated and (bar_index - ob.last_touched_bar) < SUCCESS_BARS_REQUIRED
                        ob.was_successful := false
                        ob.success_evaluated := true
                        if debugMode
                            label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level,
                                     "Failed: Broken after " + str.tostring(bar_index - ob.last_touched_bar) + " bars",
                                     color=color.rgb(255,64,64), style=label.style_label_down)
                    else
                        if debugMode
                            label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level,
                                     "Broken: " + (ob.is_support ? "Support" : "Resistance"),
                                     color=color.rgb(255,64,64), style=label.style_label_down)

            if ob.is_broken and not ob.is_mitigated
                mitigation_level = ob.is_support ? ob.upper_level - (ob.upper_level - ob.lower_level) * 0.5 : ob.lower_level + (ob.upper_level - ob.lower_level) * 0.5
                if (ob.is_support and close[1] > mitigation_level) or (not ob.is_support and close[1] < mitigation_level)
                    ob.is_mitigated := true

                    // Check if this block was touched and failed within the required period
                    if ob.last_touched_bar > 0 and not ob.success_evaluated and (bar_index - ob.last_touched_bar) < SUCCESS_BARS_REQUIRED
                        ob.was_successful := false
                        ob.success_evaluated := true
                        if debugMode
                            label.new(bar_index, ob.is_support ? ob.upper_level : ob.lower_level,
                                     "Failed: Mitigated after " + str.tostring(bar_index - ob.last_touched_bar) + " bars",
                                     color=color.rgb(128,128,128), style=label.style_label_down)
                    else
                        if debugMode
                            label.new(bar_index, ob.is_support ? ob.upper_level : ob.lower_level,
                                     "Mitigated: " + (ob.is_support ? "Support" : "Resistance"),
                                     color=color.rgb(128,128,128), style=label.style_label_down)

            // Calculate zone strength score only if needed (lazy evaluation)
            // Check if we need to recalculate (every 5 bars or if not calculated yet)
            if (not ob.strength_calculated) or (min_star_rating > 1 and not ob.meets_min_rating) or (bar_index % 5 == 0)
                // Refactored Code
                ob.strength_score := f_calculateZoneStrength(ob.volume_ratio, ob.rsi_confluence, ob.macd_confluence, ob.ma_confluence, ob.volume_confluence)
                ob.strength_calculated := true

                // Update meets_min_rating flag
                float star_rating_threshold = f_getStarRatingThreshold(min_star_rating)
                ob.meets_min_rating := ob.strength_score >= star_rating_threshold

            // Apply time filtering if enabled
            if enableTimeFilter and (bar_index - ob.created_at) > maxBlockAge
                array.push(to_remove, i)
                array.push(filtered_by_update, true)  // Track that this block was filtered by time
                continue

            // Apply smart filtering if enabled
            if useSmartFiltering and ob.strength_score < minZoneStrength and not ob.has_confluence
                array.push(to_remove, i)
                continue

            // Apply star rating filter
            if not ob.meets_min_rating
                array.push(to_remove, i)
                array.push(filtered_by_update, true)  // Track that this block was filtered
                continue
            else
                array.push(filtered_by_update, false)  // Track that this block was not filtered

            // Mark ineligible blocks for removal, but keep blocks that are hidden for display purposes
            // Now we use our dedicated flag for display-hidden blocks
            if (ob.is_broken and not showBreakerBlocks) or (ob.is_mitigated and not showMitigated and not ob.is_display_hidden)
                array.push(to_remove, i)
            else
                // Calculate transparency based on age if enabled (moved outside the conditional)
                blockAge = bar_index - ob.created_at
                int ageBasedTransparency = applyBlockAgeFading ? math.min(60 + math.round(blockAge * 0.5), 90) : 85

                // Check if this is a block that's hidden for display purposes
                if ob.is_display_hidden
                    // For hidden blocks, ensure all labels are removed
                    if not na(ob.top_price_label)
                        label.delete(ob.top_price_label)
                        ob.top_price_label := na
                    if not na(ob.bottom_price_label)
                        label.delete(ob.bottom_price_label)
                        ob.bottom_price_label := na
                    if not na(ob.confluence_label)
                        label.delete(ob.confluence_label)
                        ob.confluence_label := na
                else

                    box.set_right(ob.box, bar_index + extendObs)
                    mid_level := (ob.upper_level + ob.lower_level) / 2

                    if showMidline and not na(ob.midline)
                        line.set_x2(ob.midline, bar_index + extendObs)

                int label_x = bar_index + extendObs
                float mid_price = mid_level
                strength_pct = (total_volume != 0) ? (ob.total_volume / total_volume * 100) : 0
                buy_pct = ob.total_volume > 0 ? (ob.buy_volume / ob.total_volume) * 100 : 0
                sell_pct = ob.total_volume > 0 ? (ob.sell_volume / ob.total_volume) * 100 : 0

                // Get the appropriate icon based on buy/sell percentages
                buySellIcon = getBuySellIcon(buy_pct, sell_pct)

                // Skip label creation for blocks that are hidden for display
                if showPriceLabels and not ob.is_display_hidden
                    // Semi-transparent background for improved readability
                    labelBgColor = color.new(color.black, 20)

                    // Determine status text
                    statusText = ob.is_broken ? "⚠️ BROKEN" : ob.is_mitigated ? "⚡ MIT" : "✅"

                    // Get strength rating
                    strengthRating = showZoneStrength ? f_getColorCodedStarRating(ob.strength_score) : ""

                    // Add retest information
                    retestInfo = ob.retest_count > 0 ?
                                 "🔄 Retests: " + str.tostring(ob.retest_count) +
                                 " | ✓ " + str.tostring(math.round(ob.success_rate * 100, 0)) + "%" : ""

                    // Add block age information
                    int block_age = bar_index - ob.created_at
                    string ageInfo = "⏱️ Age: " + str.tostring(block_age) + " bars"

                    // Calculate box height for better positioning
                    box_height = math.abs(ob.upper_level - ob.lower_level)

                    // Create single consolidated label with fixed positioning
                    if ob.is_support
                        if not na(ob.top_price_label)
                            label.delete(ob.top_price_label)
                            ob.top_price_label := na

                        if not na(ob.bottom_price_label)
                            label.delete(ob.bottom_price_label)
                            ob.bottom_price_label := na

                        // Position labels directly at the block boundary
                        string labelText = ""

                        // Determine label content based on display mode
                        if displayModeOpt == "Compact"
                            // Compact mode - only essential information including strength score
                            labelText := "SUP " + str.tostring(mid_price, "#.###") + 
                                         " | " + str.tostring(math.round(ob.strength_score, 1)) + "pts" +
                                         (showZoneStrength ? " " + strengthRating : "")
                        else
                            // Detailed mode - comprehensive information including strength score
                            labelText := "📍 SUP " + statusText + " | " + str.tostring(mid_price, "#.###") +
                                         " | " + str.tostring(math.round(ob.strength_score, 1)) + "pts" +
                                         (showZoneStrength ? " | " + strengthRating : "") + "\n" +
                                         "📊 VOL: " + formatVolume(ob.total_volume) + " | 💪 " + str.tostring(math.round(strength_pct, 1)) + "%\n" +
                                         buySellIcon + " " + (buy_pct > sell_pct ? "BUY" : "SELL") + " " + str.tostring(math.round(math.max(buy_pct, sell_pct), 1)) + "% | B:" + formatVolume(ob.buy_volume) + " S:" + formatVolume(ob.sell_volume) +
                                         (ob.retest_count > 0 ? "\n" + retestInfo : "") +
                                         "\n" + ageInfo

                        ob.bottom_price_label := label.new(
                             label_x, ob.lower_level,
                             labelText,
                             color=labelBgColor,
                             style=label.style_label_left,
                             textcolor=color.white,
                             size=orderBlockTextSize,
                             yloc=yloc.price
                             )
                    else
                        if not na(ob.bottom_price_label)
                            label.delete(ob.bottom_price_label)
                            ob.bottom_price_label := na

                        if not na(ob.top_price_label)
                            label.delete(ob.top_price_label)
                            ob.top_price_label := na

                        // Position labels directly at the block boundary
                        string labelText = ""

                        // Determine label content based on display mode
                        if displayModeOpt == "Compact"
                            // Compact mode - only essential information including strength score
                            labelText := "RES " + str.tostring(mid_price, "#.###") + 
                                         " | " + str.tostring(math.round(ob.strength_score, 1)) + "pts" +
                                         (showZoneStrength ? " " + strengthRating : "")
                        else
                            // Detailed mode - comprehensive information including strength score
                            labelText := "📍 RES " + statusText + " | " + str.tostring(mid_price, "#.###") +
                                         " | " + str.tostring(math.round(ob.strength_score, 1)) + "pts" +
                                         (showZoneStrength ? " | " + strengthRating : "") + "\n" +
                                         "📊 VOL: " + formatVolume(ob.total_volume) + " | 💪 " + str.tostring(math.round(strength_pct, 1)) + "%\n" +
                                         buySellIcon + " " + (buy_pct > sell_pct ? "BUY" : "SELL") + " " + str.tostring(math.round(math.max(buy_pct, sell_pct), 1)) + "% | B:" + formatVolume(ob.buy_volume) + " S:" + formatVolume(ob.sell_volume) +
                                         (ob.retest_count > 0 ? "\n" + retestInfo : "") +
                                         "\n" + ageInfo

                        ob.top_price_label := label.new(
                             label_x, ob.upper_level,
                             labelText,
                             color=labelBgColor,
                             style=label.style_label_left,
                             textcolor=color.white,
                             size=orderBlockTextSize,
                             yloc=yloc.price
                             )

                // Dynamic color handling with conditional age-based transparency
                if applyBlockAgeFading
                    final_box_color = ob.is_mitigated and showMitigated ?
                         color.new(mitigatedColor, ageBasedTransparency) :
                         ob.is_broken and showBreakerBlocks ?
                         color.new(breakoutColor, ageBasedTransparency) :
                         ob.has_confluence and highlightConfluence ?
                         color.new(ob.is_support ? color.rgb(0, 255, 128, 70) : color.rgb(255, 128, 0, 70), ageBasedTransparency) :
                         ob.is_support ?
                         color.new(bullColor, ageBasedTransparency) :
                         color.new(bearColor, ageBasedTransparency)
                    box.set_bgcolor(ob.box, final_box_color)
                else
                    final_box_color = ob.is_mitigated and showMitigated ?
                         mitigatedColor :
                         ob.is_broken and showBreakerBlocks ?
                         breakoutColor :
                         ob.has_confluence and highlightConfluence ?
                         (ob.is_support ? color.rgb(0, 255, 128, 70) : color.rgb(255, 128, 0, 70)) :
                         ob.is_support ?
                         bullColor :
                         bearColor
                    box.set_bgcolor(ob.box, final_box_color)

                // Enhanced border styling based on volume ratio and strength
                border_color = ob.has_confluence ?
                     (ob.is_support ? color.rgb(0,255,128) : color.rgb(255,128,0)) :
                     getBuySellColor(buy_pct, sell_pct)

                box.set_border_color(ob.box, border_color)
                box.set_border_width(ob.box, orderBlockBorderWidth)
                box.set_border_style(ob.box, ob.is_broken ? line.style_dashed : orderBlockBorderStyle)
                array.set(order_blocks, i, ob)

        // Remove ineligible blocks
        if array.size(to_remove) > 0
            for i = array.size(to_remove) - 1 to 0 by 1
                idx = array.get(to_remove, i)
                if idx >= 0 and idx < array.size(order_blocks)
                    ob = array.get(order_blocks, idx)
                    f_cleanupOrderBlock(ob)
                    array.remove(order_blocks, idx)
                    if debugMode
                        label.new(bar_index, close, "Removed Block: " + (ob.is_support ? "Support" : "Resistance"), color=color.orange, style=label.style_label_down)

// Hide overlapping blocks with enhanced priority logic
hideOverlappingBlocks() =>
    if hideOverlaps and array.size(order_blocks) > 1
        to_remove = array.new_int(0)
        for i = array.size(order_blocks) - 1 to 1 by 1
            if not array.includes(to_remove, i)
                ob1 = array.get(order_blocks, i)
                for j = i - 1 to 0 by 1
                    if not array.includes(to_remove, j)
                        ob2 = array.get(order_blocks, j)
                        if ob1.lower_level <= ob2.upper_level and ob1.upper_level >= ob2.lower_level
                            // Enhanced priority logic considering confluence and strength
                            if ob1.is_broken and not ob2.is_broken
                                array.push(to_remove, i)
                            else if ob2.is_broken and not ob1.is_broken
                                array.push(to_remove, j)
                            else if prioritizeConfluence  // Apply stronger confluence prioritization if enabled
                                // Count confluence factors for each block
                                int ob1_conf_count = (ob1.rsi_confluence ? 1 : 0) +
                                                     (ob1.macd_confluence ? 1 : 0) +
                                                     (ob1.ma_confluence ? 1 : 0) +
                                                     (ob1.volume_confluence ? 1 : 0)

                                int ob2_conf_count = (ob2.rsi_confluence ? 1 : 0) +
                                                     (ob2.macd_confluence ? 1 : 0) +
                                                     (ob2.ma_confluence ? 1 : 0) +
                                                     (ob2.volume_confluence ? 1 : 0)

                                // Prioritize blocks with more confluence factors
                                if ob1_conf_count > ob2_conf_count
                                    array.push(to_remove, j)
                                else if ob2_conf_count > ob1_conf_count
                                    array.push(to_remove, i)
                                else if ob1.has_confluence and not ob2.has_confluence
                                    array.push(to_remove, j)
                                else if ob2.has_confluence and not ob1.has_confluence
                                    array.push(to_remove, i)
                                else if ob1.strength_score > ob2.strength_score + 1.5  // Reduced threshold for strength difference
                                    array.push(to_remove, j)
                                else if ob2.strength_score > ob1.strength_score + 1.5
                                    array.push(to_remove, i)
                                else if ob1.retest_count > ob2.retest_count and ob1.success_rate >= 0.5
                                    array.push(to_remove, j)
                                else if ob2.retest_count > ob1.retest_count and ob2.success_rate >= 0.5
                                    array.push(to_remove, i)
                                else if ob1.total_volume < ob2.total_volume
                                    array.push(to_remove, i)
                                else
                                    array.push(to_remove, j)
                            else  // Original priority logic
                                if ob1.has_confluence and not ob2.has_confluence
                                    array.push(to_remove, j)
                                else if ob2.has_confluence and not ob1.has_confluence
                                    array.push(to_remove, i)
                                else if ob1.strength_score > ob2.strength_score + 2.0  // Significant strength difference
                                    array.push(to_remove, j)
                                else if ob2.strength_score > ob1.strength_score + 2.0  // Significant strength difference
                                    array.push(to_remove, i)
                                else if ob1.retest_count > ob2.retest_count and ob1.success_rate >= 0.5
                                    array.push(to_remove, j)
                                else if ob2.retest_count > ob1.retest_count and ob2.success_rate >= 0.5
                                    array.push(to_remove, i)
                                else if ob1.total_volume < ob2.total_volume
                                    array.push(to_remove, i)
                                else
                                    array.push(to_remove, j)
        if array.size(to_remove) > 0
            for k = array.size(to_remove) - 1 to 0 by 1
                idx = array.get(to_remove, k)
                if idx >= 0 and idx < array.size(order_blocks)
                    ob = array.get(order_blocks, idx)
                    f_cleanupOrderBlock(ob)
                    array.remove(order_blocks, idx)
                    if debugMode
                        label.new(bar_index, close, "Overlap Removed: " + (ob.is_support ? "Support" : "Resistance") +
                                 "\nStrength: " + str.tostring(math.round(ob.strength_score, 1)) +
                                 " | Conf: " + (ob.has_confluence ? "Yes" : "No"),
                                 color=color.rgb(180,100,255), style=label.style_label_down)
    order_blocks
// ====================================================================
// FUNCTIONS FOR BLOCK SORTING (Using a reliable Insertion Sort)
// This non-recursive method is guaranteed to compile and work correctly.
// ====================================================================

// Helper function to calculate the composite score for a single OrderBlock.
_calculateCompositeScore(OrderBlock ob, int current_bar_index) =>
    float recency_score = math.max(0.0, 1.0 - (current_bar_index - ob.created_at) / 500.0)
    float strength_score = ob.strength_score / 10.0
    int confluence_count = (ob.rsi_confluence ? 1 : 0) + (ob.macd_confluence ? 1 : 0) + (ob.ma_confluence ? 1 : 0) + (ob.volume_confluence ? 1 : 0)
    float confluence_score = confluence_count / 4.0
    recency_score * 0.4 + strength_score * 0.4 + confluence_score * 0.2


// Main function to select top blocks for display.
// This function sorts the blocks using a reliable, non-recursive Insertion Sort.
selectTopBlocksForDisplay() =>
    int num_blocks = array.size(order_blocks)
    if num_blocks <= max_blocks
        // No need to sort or filter, return an empty array.
        array.new<OrderBlock>()
    else
        // Create a mutable copy of the order_blocks array to be sorted.
        array<OrderBlock> display_blocks = array.copy(order_blocks)

        // --- Insertion Sort Logic (sorts in descending order) ---
        for i = 1 to num_blocks - 1
            // The current block to be placed in the sorted part of the array.
            OrderBlock key_block = array.get(display_blocks, i)
            float key_score = _calculateCompositeScore(key_block, bar_index)

            // Start comparing with the element before it.
            int j = i - 1

            // Move elements of display_blocks[0...i-1] that have a score
            // smaller than the key_score one position to their right.
            while j >= 0 and _calculateCompositeScore(array.get(display_blocks, j), bar_index) < key_score
                array.set(display_blocks, j + 1, array.get(display_blocks, j))
                j := j - 1
            
            // Place the key_block at its correct sorted position.
            array.set(display_blocks, j + 1, key_block)

        // The 'display_blocks' array is now fully sorted in descending order.
        // The blocks to hide are all elements from the 'max_blocks' index to the end.
        array.slice(display_blocks, max_blocks)
// ====================================================================
// MAIN LOGIC
// ====================================================================
// Reset filtered blocks tracking arrays each bar
array.clear(filtered_by_create)
array.clear(filtered_by_update)

// Clean up any orphaned visual elements on first bar
if barstate.isfirst
    // Clear any existing boxes and labels that might be left over
    int blocks_count = array.size(order_blocks)
    if blocks_count > 0  // Only process if array has elements
        for i = 0 to blocks_count - 1
            ob = array.get(order_blocks, i)
            f_cleanupOrderBlock(ob)
    // Always clear the array, even if it's empty
    array.clear(order_blocks)

volSMA := ta.sma(math.abs(upAndDownVolume()), vol_len)

float ema200_val = ta.ema(ema200_source, ema200_length)

if debugMode
    // Extract function calls for consistency
    int debug_regime = f_detectMarketRegime()

    // Enhanced debug info for troubleshooting
    string regime_text = debug_regime == 1 ? "Trending" : debug_regime == 2 ? "Volatile" : "Ranging"

    string debug_info = "Lookback: " + str.tostring(lookbackPeriod) +
                 "\nRegime: " + regime_text

    label.new(bar_index, high, debug_info, style=label.style_none, color=color.new(color.white, 0), textcolor=color.white)

// Calculate support and resistance levels.
[supportLevel, supportLevelAlt, resistanceLevel, resistanceLevelAlt, sup_color, res_color, breakout_res, res_holds, sup_holds, breakout_sup, Vol] = calcSupportResistance(close, lookbackPeriod, math.abs(upAndDownVolume()))

if not na(supportLevel) and Vol > volSMA * volMultiplier and (not useTrendFilter or close > ema200_val)
    buyVol = 0.0
    sellVol = 0.0
    totalVol = 0.0
    for i = 1 to lookbackPeriod
        totalVol += volume[i]
        if close[i] > open[i]
            buyVol += volume[i]
        else if close[i] < open[i]
            sellVol += volume[i]
    createOrderBlock(true, supportLevel, supportLevelAlt, totalVol, buyVol, sellVol, sup_color)

if not na(resistanceLevel) and Vol > volSMA * volMultiplier and (not useTrendFilter or close < ema200_val)
    buyVol = 0.0
    sellVol = 0.0
    totalVol = 0.0
    for i = 1 to lookbackPeriod
        totalVol += volume[i]
        if close[i] > open[i]
            buyVol += volume[i]
        else if close[i] < open[i]
            sellVol += volume[i]
    createOrderBlock(false, resistanceLevel, resistanceLevelAlt, totalVol, buyVol, sellVol, res_color)

// First, hide overlapping blocks to reduce visual clutter
hideOverlappingBlocks()

// Then, update all blocks (including hidden ones) for analysis
updateOrderBlocks()

// Finally, select top blocks for display based on our criteria
// This allows us to analyze more blocks but only display the most relevant ones
blocks_to_hide = selectTopBlocksForDisplay()

// Hide visual elements for blocks that exceed the display limit
if array.size(blocks_to_hide) > 0
    for ob in blocks_to_hide
        // Instead of trying to hide existing elements, we'll remove them and track that we've done so
        // This is more reliable than trying to make them transparent
        f_cleanupOrderBlock(ob)

        // Mark the block as hidden for display purposes but kept for analysis
        ob.is_display_hidden := true  // Use our new dedicated flag

        // Make sure we don't confuse this with actually mitigated blocks
        ob.is_mitigated := false

    if debugMode and array.size(blocks_to_hide) > 0
        label.new(bar_index, close, "Hidden for display: " + str.tostring(array.size(blocks_to_hide)) + " blocks",
                 color=color.new(color.blue, 70), style=label.style_label_down, textcolor=color.white)

// Count filtered blocks from the tracking arrays
int filtered_count = 0

// Only process arrays if they have elements
if array.size(filtered_by_create) > 0
    for i = 0 to array.size(filtered_by_create) - 1
        if array.get(filtered_by_create, i)
            filtered_count += 1

if array.size(filtered_by_update) > 0
    for i = 0 to array.size(filtered_by_update) - 1
        if array.get(filtered_by_update, i)
            filtered_count += 1

// Update the global filtered blocks count
filtered_blocks_count := filtered_count

// Add message if orderblocks are being created but not displayed
// Show a more prominent warning in debug mode, and a subtle one in normal mode
if array.size(order_blocks) == 0 and filtered_blocks_count > 0
    if debugMode
        // Detailed warning in debug mode
        label.new(bar_index, high + atr * 3, "⚠️ All orderblocks filtered out by star rating filter! Try lowering the minimum star rating.",
                  color=color.new(color.red, 20),
                  style=label.style_label_down,
                  textcolor=color.white,
                  size=size.normal)
    else
        // Subtle warning in normal mode - only show once every 20 bars to avoid clutter
        if bar_index % 20 == 0
            label.new(bar_index, high + atr * 1.5, "⚠️ Blocks filtered by " + star_selection + " rating",
                      color=color.new(color.black, 70),
                      style=label.style_label_down,
                      textcolor=color.new(color.white, 20),
                      size=size.small)

// ====================================================================
// REVERSAL PATTERN DETECTION
// ====================================================================
bullish_reversal = (high[1] > high) and (close[1] < close) and (open[1] < open) and barstate.isconfirmed
bearish_reversal = (low[1] < low) and (close[1] > close) and (open[1] > open) and barstate.isconfirmed

// Enhanced reversal triangles with better visibility
plotshape(
     series = showReversalTriangles and bullish_reversal ? 1 : na,
     title="Bullish Reversal",
     location=location.abovebar,
     color=bullishReversalColor,
     style=shape.triangledown,
     size=size.tiny
     )

plotshape(
     series = showReversalTriangles and bearish_reversal ? 1 : na,
     title="Bearish Reversal",
     location=location.belowbar,
     color=bearishReversalColor,
     style=shape.triangleup,
     size=size.tiny
     )

// ====================================================================
// DEBUG MODE (Enhanced Debug Information)
// ====================================================================
// Create star rating statistics panel
// Calculate number of rows needed based on min_star_rating (header + ratings from min to 5 + description)
var int table_rows = 7  // Default value for initialization
table_rows := 3 + (6 - min_star_rating)  // Header + ratings from min to 5 + description

var table starStatsTable = table.new(
     position.bottom_left,
     3, table_rows,
     bgcolor = color.new(color.black, 30),
     border_width = 1,
     border_color = color.new(color.white, 70)
     )

// Initialize the star stats table
table.cell(starStatsTable, 0, 0, "⭐ Rating", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
table.cell(starStatsTable, 1, 0, "Count", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
table.cell(starStatsTable, 2, 0, "Success", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)

// Add description of success criteria and analysis information
table.cell(starStatsTable, 0, table_rows - 1, "Success: Block intact " + str.tostring(SUCCESS_BARS_REQUIRED) + " bars after touch" +
          "\nAnalyzing up to " + str.tostring(analysis_blocks) + " blocks",
          text_color=color.rgb(0,255,128), bgcolor=color.new(color.black, 20), text_size=tableTextSize)

// Initialize star rating counts and success tracking
var int[] star_counts = array.new_int(5, 0)
var int[] star_success = array.new_int(5, 0)
var int[] star_total = array.new_int(5, 0)

// Reset counts each bar
for i = 0 to 4
    array.set(star_counts, i, 0)

// Count blocks by star rating - include all blocks kept for analysis
if array.size(order_blocks) > 0
    for ob in order_blocks
        // Include all blocks in the statistics, including those hidden for display
        star_rating = ob.strength_score >= 8.0 ? 5 :
                     ob.strength_score >= 6.0 ? 4 :
                     ob.strength_score >= 4.0 ? 3 :
                     ob.strength_score >= 2.0 ? 2 : 1

        array.set(star_counts, star_rating - 1, array.get(star_counts, star_rating - 1) + 1)

        // Track success/failure for performance stats
        // Include all blocks with success data, regardless of display status
        if ob.was_successful and ob.success_evaluated
            array.set(star_success, star_rating - 1, array.get(star_success, star_rating - 1) + 1)
            array.set(star_total, star_rating - 1, array.get(star_total, star_rating - 1) + 1)
        else if ob.is_broken or (ob.success_evaluated and not ob.was_successful)
            array.set(star_total, star_rating - 1, array.get(star_total, star_rating - 1) + 1)

    // Update star stats table
    // Only show ratings that meet or exceed the user's selected minimum star rating
    int table_row = 1  // Start at row 1 (after header)

    for i = min_star_rating - 1 to 4  // Start from min_star_rating-1 (array index) to 4 (5 stars)
        stars = i == 0 ? "⭐" : i == 1 ? "⭐⭐" : i == 2 ? "⭐⭐⭐" : i == 3 ? "⭐⭐⭐⭐" : "⭐⭐⭐⭐⭐"
        count = array.get(star_counts, i)

        // Calculate success rate
        success_rate = array.get(star_total, i) > 0 ?
                      math.round(array.get(star_success, i) / array.get(star_total, i) * 100, 0) :
                      0

        success_text = array.get(star_total, i) > 0 ?
                      str.tostring(success_rate) + "%" :
                      "N/A"

        // Set cell colors based on star rating
        color text_color = i == 4 ? color.rgb(0,255,0) :
                          i == 3 ? color.rgb(144,238,144) :
                          i == 2 ? color.rgb(255,255,0) :
                          i == 1 ? color.rgb(255,165,0) :
                          color.rgb(255,69,0)

        table.cell(starStatsTable, 0, table_row, stars, text_color=text_color, text_size=tableTextSize)
        table.cell(starStatsTable, 1, table_row, str.tostring(count), text_color=color.white, text_size=tableTextSize)
        table.cell(starStatsTable, 2, table_row, success_text, text_color=success_rate >= 70 ? color.rgb(0,255,0) :
                                                                     success_rate >= 50 ? color.rgb(255,255,0) :
                                                                     color.rgb(255,69,0), text_size=tableTextSize)
        table_row += 1  // Move to next row

if debugMode
    // Improved debug label with better styling
    var label debug_label = label.new(
         bar_index, high + atr * 2,
         "",
         color=color.new(color.black, 20),
         style=label.style_label_down,
         textcolor=color.white,
         size=size.normal
         )

    // Create a more comprehensive debug panel
    // Extract function calls for consistency
    int regime = f_detectMarketRegime()
    float adaptive_box_size = f_getAdaptiveBoxSize()
    float current_atr = ta.atr(dynamicATRPeriod)

    // Get market regime for display
    string regime_text = regime == 1 ? "Trending" : regime == 2 ? "Volatile" : "Ranging"

    // Get box size
    float effective_box_size = useDynamicBox ?
                         (adaptiveBoxSize ? adaptive_box_size / atr : dynamicBoxMultiplier) :
                         box_width

    // Count visible and hidden blocks using our dedicated flag
    int visible_blocks = 0
    int hidden_blocks = 0
    for ob in order_blocks
        if ob.is_display_hidden
            hidden_blocks += 1
        else
            visible_blocks += 1

    // Count blocks with success data
    int blocks_with_success = 0
    int blocks_with_failure = 0
    for ob in order_blocks
        if ob.success_evaluated
            if ob.was_successful
                blocks_with_success += 1
            else
                blocks_with_failure += 1

    string debug_text = "🐞 Debug Info v4.0:\n" +
                 "📊 OB Count: " + str.tostring(visible_blocks) + " visible / " + str.tostring(hidden_blocks) + " hidden / " + str.tostring(array.size(order_blocks)) + " total\n" +
                 "🔍 Analysis Blocks: " + str.tostring(analysis_blocks) + " max\n" +
                 "📊 Success Data: " + str.tostring(blocks_with_success) + " success / " + str.tostring(blocks_with_failure) + " failure\n" +
                 "📈 Vol SMA: " + str.tostring(volSMA) + "\n" +
                 "📏 ATR: " + str.tostring(atr) + "\n" +
                 "🔍 Market Regime: " + regime_text + "\n" +
                 "🔍 Lookback: " + str.tostring(lookbackPeriod) + "\n" +
                 "📏 Effective Box Size: " + str.tostring(math.round(effective_box_size, 2)) + "×ATR\n"

    // Add information about the most recent order block if available
    if array.size(order_blocks) > 0
        OrderBlock latest_ob = array.get(order_blocks, 0)
        debug_text += "📌 Last OB: " + (latest_ob.is_support ? "Support" : "Resistance") +
                     " at " + str.tostring(latest_ob.is_support ? latest_ob.upper_level : latest_ob.lower_level) + "\n" +
                     "⏱️ Created: bar " + str.tostring(latest_ob.created_at) + "\n" +
                     "📊 Volume: " + formatVolume(latest_ob.total_volume) + "\n" +
                     "💪 Strength: " + str.tostring(math.round(latest_ob.strength_score, 1)) + "/10\n" +
                     "🎯 Confluence: " + (latest_ob.has_confluence ? "Yes" : "No") + "\n" +
                     "🔄 Retests: " + str.tostring(latest_ob.retest_count) +
                     (latest_ob.retest_count > 0 ? " (Success: " + str.tostring(math.round(latest_ob.success_rate * 100, 0)) + "%)" : "") + "\n" +
                     "🔄 Status: " + (latest_ob.is_broken ? "Broken" : latest_ob.is_mitigated ? "Mitigated" : "Active") + "\n"

    // Add system settings information
    debug_text += "🔍 Lookback: " + str.tostring(lookbackPeriod) + "\n" +
                 "📏 Box Width: " + str.tostring(useDynamicBox ? (na(ta.atr(dynamicATRPeriod)) ? atr * dynamicBoxMultiplier : ta.atr(dynamicATRPeriod) * dynamicBoxMultiplier) : atr * box_width) + "\n" +
                 "🧠 Smart Filter: " + str.tostring(useSmartFiltering) + "\n" +
                 "⏰ Time Filter: " + (enableTimeFilter ? "On (" + str.tostring(maxBlockAge) + " bars)" : "Off") + "\n" +
                 "⭐ Min Star Rating: " + str.tostring(min_star_rating) + " " +
                 (min_star_rating == 5 ? "⭐⭐⭐⭐⭐" :
                  min_star_rating == 4 ? "⭐⭐⭐⭐" :
                  min_star_rating == 3 ? "⭐⭐⭐" :
                  min_star_rating == 2 ? "⭐⭐" : "⭐") + "\n" +
                 "🎯 Confluence: " + str.tostring(detectConfluence)

    label.set_text(debug_label, debug_text)
    label.set_xy(debug_label, bar_index, high + atr * 2)

    // Add a statistics table if we have enough data
    if array.size(order_blocks) >= 3
        var table statsTable = table.new(
             position.bottom_right,
             3, 3,
             bgcolor = color.new(color.black, 30),
             border_width = 1,
             border_color = color.new(color.white, 70)
             )

        // Count blocks by type
        int support_count = 0
        int resistance_count = 0
        int confluence_count = 0
        int broken_count = 0

        for ob in order_blocks
            if ob.is_support
                support_count += 1
            else
                resistance_count += 1

            if ob.has_confluence
                confluence_count += 1

            if ob.is_broken
                broken_count += 1

        // Update table with statistics
        table.cell(statsTable, 0, 0, "📊 Statistics", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
        table.cell(statsTable, 1, 0, "Support", text_color=color.rgb(0,220,128), text_size=tableTextSize)
        table.cell(statsTable, 2, 0, str.tostring(support_count), text_color=color.white, text_size=tableTextSize)

        table.cell(statsTable, 1, 1, "Resistance", text_color=color.rgb(255,100,100), text_size=tableTextSize)
        table.cell(statsTable, 2, 1, str.tostring(resistance_count), text_color=color.white, text_size=tableTextSize)

        table.cell(statsTable, 1, 2, "Confluence", text_color=color.rgb(255,215,0), text_size=tableTextSize)
        table.cell(statsTable, 2, 2, str.tostring(confluence_count), text_color=color.white, text_size=tableTextSize)

// Star rating filter table
var table starRatingTable = table.new(
     position.top_right,  // Changed to top_left to avoid overlap with lookback table
     1, 4,  // Increased rows to 4 to show dynamic adjustment info
     bgcolor = color.new(color.black, 30),
     border_width = 1,
     border_color = color.new(color.white, 70)
     )

// Get dynamic adjustment info
float base_threshold = min_star_rating == 5 ? 8.0 :
                      min_star_rating == 4 ? 6.0 :
                      min_star_rating == 3 ? 4.0 :
                      min_star_rating == 2 ? 2.0 : 0.0
float actual_threshold = f_getStarRatingThreshold(min_star_rating)
string dynamic_status = ""

if dynamicStarRating and min_star_rating > 1
    float adjustment_pct = (actual_threshold / base_threshold - 1.0) * 100
    string adjustment_dir = adjustment_pct < 0 ? "↓" : adjustment_pct > 0 ? "↑" : "="
    dynamic_status := "Dynamic: " + adjustment_dir + " " + str.tostring(math.abs(math.round(adjustment_pct, 1))) + "%"
else
    dynamic_status := "Dynamic: Off"

if debugMode
    // Update star rating filter table
    table.cell(
         starRatingTable, 0, 0,
         "⭐ Min Star Rating",
         text_color=color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 30)
         )

    table.cell(
         starRatingTable, 0, 1,
         (min_star_rating == 5 ? f_getColorCodedStarRating(8.0) :
         min_star_rating == 4 ? f_getColorCodedStarRating(6.0) :
         min_star_rating == 3 ? f_getColorCodedStarRating(4.0) :
         min_star_rating == 2 ? f_getColorCodedStarRating(2.0) : f_getColorCodedStarRating(1.0)),
         text_color=color.rgb(255,215,0),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 50)
         )

    // Add filtered blocks count
    table.cell(
         starRatingTable, 0, 2,
         "Filtered: " + str.tostring(filtered_blocks_count),
         text_color=color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 40)
         )

    // Add dynamic adjustment info
    table.cell(
         starRatingTable, 0, 3,
         dynamic_status,
         text_color=dynamicStarRating ? color.rgb(0,255,128) : color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 40)
         )



// ====================================================================
// MOVING AVERAGE PLOTTING
// ====================================================================
// Enhanced moving averages with improved visibility
plot(
     show_sma ? ta.sma(sma_source, sma_length) : na,
     "SMA",
     color=sma_color,
     linewidth=2,
     style=plot.style_line
     )

plot(
     show_ema50 ? ta.ema(ema50_source, ema50_length) : na,
     "EMA 50",
     color=ema50_color,
     linewidth=2,
     style=plot.style_line
     )

plot(
     show_ema200 ? ta.ema(ema200_source, ema200_length) : na,
     "EMA 200",
     color=ema200_color,
     linewidth=2,
     style=plot.style_line
     )

// ====================================================================
// ALERT CONDITIONS
// ====================================================================
// Basic alerts
alertcondition(breakout_res or breakout_sup, title="Order Block Break", message="An order block has been broken!")
alertcondition(bullish_reversal or bearish_reversal, title="Reversal Pattern", message="A reversal pattern has been detected!")

// New confluence alerts
var bool newConfluenceZone = false
if array.size(order_blocks) > 0
    OrderBlock ob = array.get(order_blocks, 0)
    newConfluenceZone := ob.has_confluence and bar_index == ob.created_at
else
    newConfluenceZone := false

alertcondition(newConfluenceZone, title="New Confluence Zone", message="A new zone with confluence factors has been detected!")

// Retest alerts
var bool zoneRetest = false
if array.size(order_blocks) > 0
    for ob in order_blocks
        float mid_level = (ob.upper_level + ob.lower_level) / 2
        float price_range = math.abs(ob.upper_level - ob.lower_level)

        if ob.is_support and not ob.is_broken
            zoneRetest := low <= ob.upper_level and low >= ob.upper_level - price_range * 0.25
            if zoneRetest
                break
        else if not ob.is_support and not ob.is_broken
            zoneRetest := high >= ob.lower_level and high <= ob.lower_level + price_range * 0.25
            if zoneRetest
                break
else
    zoneRetest := false

alertcondition(zoneRetest, title="Zone Retest", message="Price is retesting a support/resistance zone!")